import { Injectable, inject } from '@angular/core';
import { CanActivate, CanDeactivate, ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { ToastService } from '../services/toast.service';
import { createLogger } from '../utils';

/**
 * ENTERPRISE: Navigation flow guard for ensuring proper user journey
 * Prevents users from accessing routes out of sequence or without proper context
 */
@Injectable({
  providedIn: 'root'
})
export class NavigationFlowGuard implements CanActivate {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly logger = createLogger('NavigationFlowGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    const routeType = route.data?.['routeType'] || 'unknown';
    const currentUrl = this.router.url;

    this.logger.info('Checking navigation flow:', { routeType, currentUrl });

    // Skip flow validation for certain routes
    if (this.shouldSkipFlowValidation(route)) {
      this.logger.info('Skipping flow validation for route');
      return of(true);
    }

    // Validate navigation flow based on route type
    const isValidFlow = this.validateNavigationFlow(routeType, currentUrl, route);
    
    if (!isValidFlow.valid) {
      this.logger.warn('Invalid navigation flow:', isValidFlow.reason);
      this.toastService.info(isValidFlow.message || 'Please start from the main page');
      return of(this.router.createUrlTree([isValidFlow.redirectTo || '/experience/main']));
    }

    this.logger.info('Navigation flow validation passed');
    return of(true);
  }

  /**
   * Check if flow validation should be skipped
   */
  private shouldSkipFlowValidation(route: ActivatedRouteSnapshot): boolean {
    return route.data?.['skipGuards'] || 
           route.data?.['skipFlowValidation'] ||
           route.data?.['isProjectLoading'];
  }

  /**
   * Validate navigation flow based on route type and context
   */
  private validateNavigationFlow(
    routeType: string, 
    currentUrl: string, 
    route: ActivatedRouteSnapshot
  ): { valid: boolean; reason?: string; message?: string; redirectTo?: string } {
    
    switch (routeType) {
      case 'ui-design-new':
      case 'application-new':
        // New project creation - always allowed
        return { valid: true };

      case 'ui-design-project':
      case 'application-project':
      case 'project-direct':
        // Project viewing - check if project ID is valid
        const projectId = route.paramMap.get('projectId');
        if (!projectId) {
          return {
            valid: false,
            reason: 'No project ID provided',
            message: 'Invalid project URL',
            redirectTo: '/experience/main'
          };
        }
        return { valid: true };

      case 'ui-design-edit':
      case 'application-edit':
      case 'project-edit':
        // Edit mode - ensure user came from project view or has direct access
        return this.validateEditAccess(route);

      case 'ui-design-preview':
      case 'application-preview':
        // Preview mode - ensure user has context
        return this.validatePreviewAccess(route);

      case 'project-share':
        // Share mode - ensure user has ownership context
        return this.validateShareAccess(route);

      default:
        // Unknown route type - allow but log
        this.logger.warn('Unknown route type for flow validation:', routeType);
        return { valid: true };
    }
  }

  /**
   * Validate edit access flow
   */
  private validateEditAccess(route: ActivatedRouteSnapshot): { valid: boolean; reason?: string; message?: string; redirectTo?: string } {
    const projectId = route.paramMap.get('projectId');
    
    if (!projectId) {
      return {
        valid: false,
        reason: 'No project ID for edit',
        message: 'Cannot edit project without valid ID',
        redirectTo: '/experience/main'
      };
    }

    // TODO: Check if user has edit permissions
    // For now, allow edit if project ID is present
    return { valid: true };
  }

  /**
   * Validate preview access flow
   */
  private validatePreviewAccess(route: ActivatedRouteSnapshot): { valid: boolean; reason?: string; message?: string; redirectTo?: string } {
    const projectId = route.paramMap.get('projectId');
    
    if (!projectId) {
      return {
        valid: false,
        reason: 'No project ID for preview',
        message: 'Cannot preview project without valid ID',
        redirectTo: '/experience/main'
      };
    }

    return { valid: true };
  }

  /**
   * Validate share access flow
   */
  private validateShareAccess(route: ActivatedRouteSnapshot): { valid: boolean; reason?: string; message?: string; redirectTo?: string } {
    const projectId = route.paramMap.get('projectId');
    
    if (!projectId) {
      return {
        valid: false,
        reason: 'No project ID for share',
        message: 'Cannot share project without valid ID',
        redirectTo: '/experience/main'
      };
    }

    // TODO: Check if user has share permissions
    return { valid: true };
  }
}

/**
 * ENTERPRISE: Unsaved changes guard for preventing data loss
 * Warns users before leaving pages with unsaved changes
 */
export interface CanComponentDeactivate {
  canDeactivate(): Observable<boolean> | Promise<boolean> | boolean;
  hasUnsavedChanges?(): boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UnsavedChangesGuard implements CanDeactivate<CanComponentDeactivate> {
  private readonly logger = createLogger('UnsavedChangesGuard');

  canDeactivate(
    component: CanComponentDeactivate,
    currentRoute: ActivatedRouteSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    this.logger.info('Checking for unsaved changes');

    // Check if component has unsaved changes
    if (component.hasUnsavedChanges && component.hasUnsavedChanges()) {
      this.logger.warn('Unsaved changes detected');
      
      // Show confirmation dialog
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this page?'
      );
      
      if (!confirmed) {
        this.logger.info('User cancelled navigation due to unsaved changes');
      }
      
      return confirmed;
    }

    // If component implements canDeactivate, use it
    if (component.canDeactivate) {
      return component.canDeactivate();
    }

    this.logger.info('No unsaved changes, allowing navigation');
    return true;
  }
}

/**
 * ENTERPRISE: Rate limiting guard for preventing abuse
 * Limits the frequency of certain actions to prevent spam/abuse
 */
@Injectable({
  providedIn: 'root'
})
export class RateLimitGuard implements CanActivate {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly logger = createLogger('RateLimitGuard');
  
  // Track action timestamps per user/session
  private readonly actionTimestamps = new Map<string, number[]>();
  
  // Rate limit configurations
  private readonly rateLimits = {
    'project-creation': { maxActions: 5, windowMs: 60000 }, // 5 projects per minute
    'project-edit': { maxActions: 10, windowMs: 60000 },    // 10 edits per minute
    'project-share': { maxActions: 3, windowMs: 60000 },    // 3 shares per minute
    'default': { maxActions: 20, windowMs: 60000 }          // 20 actions per minute
  };

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    const routeType = route.data?.['routeType'] || 'default';
    const actionType = this.getActionType(routeType);
    
    this.logger.info('Checking rate limit for action:', actionType);

    // Skip rate limiting for certain routes
    if (route.data?.['skipRateLimit']) {
      this.logger.info('Skipping rate limit for route');
      return of(true);
    }

    const isAllowed = this.checkRateLimit(actionType);
    
    if (!isAllowed) {
      this.logger.warn('Rate limit exceeded for action:', actionType);
      this.toastService.error('Too many requests. Please wait a moment before trying again.');
      return of(this.router.createUrlTree(['/experience/main']));
    }

    this.logger.info('Rate limit check passed');
    return of(true);
  }

  /**
   * Get action type from route type
   */
  private getActionType(routeType: string): string {
    if (routeType.includes('new')) return 'project-creation';
    if (routeType.includes('edit')) return 'project-edit';
    if (routeType.includes('share')) return 'project-share';
    return 'default';
  }

  /**
   * Check if action is within rate limit
   */
  private checkRateLimit(actionType: string): boolean {
    const now = Date.now();
    const config = this.rateLimits[actionType] || this.rateLimits.default;
    
    // Get or create timestamp array for this action type
    if (!this.actionTimestamps.has(actionType)) {
      this.actionTimestamps.set(actionType, []);
    }
    
    const timestamps = this.actionTimestamps.get(actionType)!;
    
    // Remove old timestamps outside the window
    const cutoff = now - config.windowMs;
    const recentTimestamps = timestamps.filter(timestamp => timestamp > cutoff);
    
    // Check if we're within the limit
    if (recentTimestamps.length >= config.maxActions) {
      return false;
    }
    
    // Add current timestamp and update the map
    recentTimestamps.push(now);
    this.actionTimestamps.set(actionType, recentTimestamps);
    
    return true;
  }

  /**
   * Clear rate limit data (useful for testing or admin override)
   */
  clearRateLimits(): void {
    this.actionTimestamps.clear();
    this.logger.info('Rate limit data cleared');
  }
}
