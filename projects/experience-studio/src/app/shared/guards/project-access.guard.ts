import { Injectable, inject } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router, UrlTree } from '@angular/router';
import { Observable, of, catchError, map } from 'rxjs';
import { RecentProjectService } from '../services/recent-project-services/recent-project.service';
import { ToastService } from '../services/toast.service';
import { UuidService } from '../services/uuid.service';
import { createLogger } from '../utils';

/**
 * ENTERPRISE: Project access guard for validating project access permissions
 * Ensures users can only access projects they own or have permission to view
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectAccessGuard implements CanActivate {
  private readonly recentProjectService = inject(RecentProjectService);
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly uuidService = inject(UuidService);
  private readonly logger = createLogger('ProjectAccessGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    const projectId = route.paramMap.get('projectId');
    const routeType = route.data?.['routeType'] || 'view';

    this.logger.info('Checking project access:', { projectId, routeType });

    // Validate project ID format
    if (!projectId || !this.uuidService.isValidProjectId(projectId)) {
      this.logger.warn('Invalid project ID format:', projectId);
      this.toastService.error('Invalid project ID');
      return of(this.router.createUrlTree(['/experience/main']));
    }

    // Skip guards if explicitly set in route data
    if (route.data?.['skipGuards']) {
      this.logger.info('Skipping guards for route:', route.routeConfig?.path);
      return of(true);
    }

    // Check project access
    return this.checkProjectAccess(projectId, routeType).pipe(
      map(hasAccess => {
        if (hasAccess) {
          this.logger.info('Project access granted:', projectId);
          return true;
        } else {
          this.logger.warn('Project access denied:', projectId);
          this.toastService.error('You do not have permission to access this project');
          return this.router.createUrlTree(['/experience/main']);
        }
      }),
      catchError(error => {
        this.logger.error('Error checking project access:', error);
        this.toastService.error('Failed to verify project access');
        return of(this.router.createUrlTree(['/experience/main']));
      })
    );
  }

  /**
   * Check if user has access to the project
   */
  private checkProjectAccess(projectId: string, action: string): Observable<boolean> {
    // For now, check if project exists and belongs to user
    // In a real implementation, this would check user permissions
    return this.recentProjectService.getProjectById(projectId).pipe(
      map(response => {
        if (!response || !response.project) {
          this.logger.warn('Project not found:', projectId);
          return false;
        }

        // TODO: Implement actual permission checking
        // For now, if project exists and user can fetch it, they have access
        // In production, you would check:
        // - User ownership
        // - Shared permissions
        // - Organization access
        // - Role-based permissions

        this.logger.info('Project access check passed:', {
          projectId,
          projectName: response.project.project_name,
          action
        });

        return true;
      }),
      catchError(error => {
        this.logger.error('Project access check failed:', error);
        return of(false);
      })
    );
  }
}

/**
 * ENTERPRISE: Project ownership guard for edit/delete operations
 * Ensures only project owners can perform destructive operations
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectOwnershipGuard implements CanActivate {
  private readonly recentProjectService = inject(RecentProjectService);
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly uuidService = inject(UuidService);
  private readonly logger = createLogger('ProjectOwnershipGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    const projectId = route.paramMap.get('projectId');
    const routeType = route.data?.['routeType'] || 'view';

    this.logger.info('Checking project ownership:', { projectId, routeType });

    // Only apply to edit/delete operations
    const restrictedActions = ['edit', 'delete', 'share'];
    const action = this.extractActionFromRouteType(routeType);
    
    if (!restrictedActions.includes(action)) {
      this.logger.info('Non-restricted action, allowing access:', action);
      return of(true);
    }

    // Validate project ID
    if (!projectId || !this.uuidService.isValidProjectId(projectId)) {
      this.logger.warn('Invalid project ID for ownership check:', projectId);
      this.toastService.error('Invalid project ID');
      return of(this.router.createUrlTree(['/experience/main']));
    }

    // Check ownership
    return this.checkProjectOwnership(projectId).pipe(
      map(isOwner => {
        if (isOwner) {
          this.logger.info('Project ownership confirmed:', projectId);
          return true;
        } else {
          this.logger.warn('Project ownership denied:', projectId);
          this.toastService.error('You do not have permission to modify this project');
          // Redirect to view mode instead of home
          const viewRoute = route.routeConfig?.path?.replace(/\/(edit|share)$/, '') || '/experience/main';
          return this.router.createUrlTree([viewRoute.replace(':projectId', projectId)]);
        }
      }),
      catchError(error => {
        this.logger.error('Error checking project ownership:', error);
        this.toastService.error('Failed to verify project ownership');
        return of(this.router.createUrlTree(['/experience/main']));
      })
    );
  }

  /**
   * Extract action from route type
   */
  private extractActionFromRouteType(routeType: string): string {
    if (routeType.includes('edit')) return 'edit';
    if (routeType.includes('share')) return 'share';
    if (routeType.includes('delete')) return 'delete';
    return 'view';
  }

  /**
   * Check if user owns the project
   */
  private checkProjectOwnership(projectId: string): Observable<boolean> {
    return this.recentProjectService.getProjectById(projectId).pipe(
      map(response => {
        if (!response || !response.project) {
          return false;
        }

        // TODO: Implement actual ownership checking
        // For now, assume user owns all projects they can access
        // In production, you would check:
        // - Project creator/owner field
        // - User ID matching
        // - Admin permissions
        // - Organization ownership

        this.logger.info('Project ownership check passed:', {
          projectId,
          projectName: response.project.project_name
        });

        return true;
      }),
      catchError(error => {
        this.logger.error('Project ownership check failed:', error);
        return of(false);
      })
    );
  }
}

/**
 * ENTERPRISE: Route validation guard for URL integrity
 * Validates route parameters and prevents malicious URL manipulation
 */
@Injectable({
  providedIn: 'root'
})
export class RouteValidationGuard implements CanActivate {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly uuidService = inject(UuidService);
  private readonly logger = createLogger('RouteValidationGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> {
    this.logger.info('Validating route:', route.url.join('/'));

    // Validate project ID format if present
    const projectId = route.paramMap.get('projectId');
    if (projectId && !this.uuidService.isValidProjectId(projectId)) {
      this.logger.warn('Invalid project ID format in route:', projectId);
      this.toastService.error('Invalid project URL');
      return of(this.router.createUrlTree(['/experience/main']));
    }

    // Validate route structure
    const routePath = route.routeConfig?.path || '';
    if (!this.isValidRouteStructure(routePath)) {
      this.logger.warn('Invalid route structure:', routePath);
      this.toastService.error('Invalid URL structure');
      return of(this.router.createUrlTree(['/experience/main']));
    }

    // Check for suspicious query parameters
    const queryParams = route.queryParams;
    if (this.hasSuspiciousParams(queryParams)) {
      this.logger.warn('Suspicious query parameters detected:', queryParams);
      // Clean the URL by removing suspicious params
      const cleanUrl = route.url.join('/');
      return of(this.router.createUrlTree([cleanUrl]));
    }

    this.logger.info('Route validation passed');
    return of(true);
  }

  /**
   * Validate route structure against known patterns
   */
  private isValidRouteStructure(routePath: string): boolean {
    const validPatterns = [
      /^projects\/:[^\/]+$/,                    // projects/:projectId
      /^projects\/:[^\/]+\/(edit|share)$/,      // projects/:projectId/edit|share
      /^ui-design\/new$/,                       // ui-design/new
      /^ui-design\/:[^\/]+$/,                   // ui-design/:projectId
      /^ui-design\/:[^\/]+\/(preview|edit)$/,   // ui-design/:projectId/preview|edit
      /^application\/new$/,                     // application/new
      /^application\/:[^\/]+$/,                 // application/:projectId
      /^application\/:[^\/]+\/(preview|edit)$/, // application/:projectId/preview|edit
      /^main$/,                                 // main
      /^$/                                      // empty (root)
    ];

    return validPatterns.some(pattern => pattern.test(routePath));
  }

  /**
   * Check for suspicious query parameters
   */
  private hasSuspiciousParams(queryParams: any): boolean {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i
    ];

    for (const [key, value] of Object.entries(queryParams)) {
      const paramString = `${key}=${value}`;
      if (suspiciousPatterns.some(pattern => pattern.test(paramString))) {
        return true;
      }
    }

    return false;
  }
}
