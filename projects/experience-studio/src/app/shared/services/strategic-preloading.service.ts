import { Injectable, inject } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, debounceTime } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

/**
 * Strategic Preloading Service
 *
 * Implements intelligent route preloading based on user behavior patterns:
 * - Preloads likely next routes based on current page
 * - Uses hover intent detection for link preloading
 * - Implements priority-based preloading queue
 * - Monitors user interaction patterns
 *
 * Uses Angular 19+ patterns with modern RxJS operators
 */
@Injectable({
  providedIn: 'root'
})
export class StrategicPreloadingService {
  private readonly router = inject(Router);

  // ENHANCED: Route transition patterns with new enterprise routing structure
  private readonly routePatterns = new Map<string, Array<{ route: string; probability: number }>>([
    // From main page - users typically choose between UI design and application
    ['/experience/main', [
      { route: '/experience/ui-design/new', probability: 0.6 },
      { route: '/experience/application/new', probability: 0.4 }
    ]],

    // From UI design creation - high probability of staying in workflow
    ['/experience/ui-design/new', [
      { route: '/experience/ui-design/:projectId', probability: 0.7 },
      { route: '/experience/ui-design/:projectId/preview', probability: 0.2 },
      { route: '/experience/main', probability: 0.1 }
    ]],

    // From application creation - high probability of staying in workflow
    ['/experience/application/new', [
      { route: '/experience/application/:projectId', probability: 0.7 },
      { route: '/experience/application/:projectId/preview', probability: 0.2 },
      { route: '/experience/main', probability: 0.1 }
    ]],

    // From project view - users often edit or preview
    ['/experience/ui-design/:projectId', [
      { route: '/experience/ui-design/:projectId/edit', probability: 0.4 },
      { route: '/experience/ui-design/:projectId/preview', probability: 0.3 },
      { route: '/experience/main', probability: 0.2 },
      { route: '/experience/ui-design/new', probability: 0.1 }
    ]],

    ['/experience/application/:projectId', [
      { route: '/experience/application/:projectId/edit', probability: 0.4 },
      { route: '/experience/application/:projectId/preview', probability: 0.3 },
      { route: '/experience/main', probability: 0.2 },
      { route: '/experience/application/new', probability: 0.1 }
    ]],

    // From edit mode - users often preview or go back to view
    ['/experience/ui-design/:projectId/edit', [
      { route: '/experience/ui-design/:projectId/preview', probability: 0.5 },
      { route: '/experience/ui-design/:projectId', probability: 0.3 },
      { route: '/experience/main', probability: 0.2 }
    ]],

    ['/experience/application/:projectId/edit', [
      { route: '/experience/application/:projectId/preview', probability: 0.5 },
      { route: '/experience/application/:projectId', probability: 0.3 },
      { route: '/experience/main', probability: 0.2 }
    ]],

    // From preview mode - users often edit or go back to view
    ['/experience/ui-design/:projectId/preview', [
      { route: '/experience/ui-design/:projectId/edit', probability: 0.4 },
      { route: '/experience/ui-design/:projectId', probability: 0.3 },
      { route: '/experience/main', probability: 0.3 }
    ]],

    ['/experience/application/:projectId/preview', [
      { route: '/experience/application/:projectId/edit', probability: 0.4 },
      { route: '/experience/application/:projectId', probability: 0.3 },
      { route: '/experience/main', probability: 0.3 }
    ]]
  ]);

  // Preloading queue with priorities
  private readonly preloadQueue = new Set<string>();
  private readonly preloadedRoutes = new Set<string>();

  // Hover intent tracking
  private hoverTimeouts = new Map<string, number>();
  private readonly HOVER_DELAY = 200; // ms before preloading on hover

  constructor() {
    this.setupRouteTracking();
    this.setupHoverPreloading();
  }

  /**
   * ENHANCED: Preload routes based on current location with dynamic project ID handling
   */
  preloadStrategicRoutes(currentRoute: string): void {
    // Normalize route for pattern matching (replace project IDs with placeholder)
    const normalizedRoute = this.normalizeRouteForMatching(currentRoute);
    const patterns = this.routePatterns.get(normalizedRoute);

    if (!patterns) {
      this.logger.debug('No preload patterns found for route:', normalizedRoute);
      return;
    }

    this.logger.info('Preloading strategic routes for:', normalizedRoute);

    // Sort by probability and preload high-probability routes
    const highPriorityRoutes = patterns
      .filter(pattern => pattern.probability > 0.5)
      .sort((a, b) => b.probability - a.probability);

    highPriorityRoutes.forEach(pattern => {
      const resolvedRoute = this.resolveRoutePattern(pattern.route, currentRoute);
      this.queueRoutePreload(resolvedRoute, 'high');
    });

    // Preload lower priority routes with delay
    const lowPriorityRoutes = patterns.filter(pattern => pattern.probability <= 0.5);
    setTimeout(() => {
      lowPriorityRoutes.forEach(pattern => {
        const resolvedRoute = this.resolveRoutePattern(pattern.route, currentRoute);
        this.queueRoutePreload(resolvedRoute, 'low');
      });
    }, 1000);
  }

  /**
   * ENHANCED: Normalize route for pattern matching by replacing dynamic segments
   */
  private normalizeRouteForMatching(route: string): string {
    // Replace UUID patterns with placeholder
    const uuidPattern = /\/[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}/gi;
    return route.replace(uuidPattern, '/:projectId');
  }

  /**
   * ENHANCED: Resolve route pattern with actual project ID from current route
   */
  private resolveRoutePattern(patternRoute: string, currentRoute: string): string {
    if (!patternRoute.includes(':projectId')) {
      return patternRoute;
    }

    // Extract project ID from current route
    const projectIdMatch = currentRoute.match(/([0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12})/i);

    if (projectIdMatch) {
      return patternRoute.replace(':projectId', projectIdMatch[1]);
    }

    // If no project ID found, return pattern as-is (might be for new routes)
    return patternRoute;
  }

  /**
   * Setup hover-based preloading for navigation links
   */
  setupHoverPreloading(): void {
    // Use event delegation for better performance
    document.addEventListener('mouseenter', this.handleLinkHover.bind(this), true);
    document.addEventListener('mouseleave', this.handleLinkLeave.bind(this), true);
  }

  /**
   * Handle link hover events
   */
  private handleLinkHover(event: Event): void {
    const target = event.target;

    // FIXED: Check if target is an HTMLElement and has closest method
    if (!target || !(target instanceof HTMLElement) || typeof target.closest !== 'function') {
      return;
    }

    const link = target.closest('a[routerLink], [routerLink]') as HTMLElement;

    if (!link) return;

    const routerLink = link.getAttribute('routerLink') ||
                      link.getAttribute('ng-reflect-router-link');

    if (!routerLink) return;

    // Set timeout for hover intent
    const timeoutId = window.setTimeout(() => {
      this.queueRoutePreload(routerLink, 'medium');
    }, this.HOVER_DELAY);

    this.hoverTimeouts.set(routerLink, timeoutId);
  }

  /**
   * Handle link leave events
   */
  private handleLinkLeave(event: Event): void {
    const target = event.target;

    // FIXED: Check if target is an HTMLElement and has closest method
    if (!target || !(target instanceof HTMLElement) || typeof target.closest !== 'function') {
      return;
    }

    const link = target.closest('a[routerLink], [routerLink]') as HTMLElement;

    if (!link) return;

    const routerLink = link.getAttribute('routerLink') ||
                      link.getAttribute('ng-reflect-router-link');

    if (!routerLink) return;

    // Clear hover timeout
    const timeoutId = this.hoverTimeouts.get(routerLink);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.hoverTimeouts.delete(routerLink);
    }
  }

  /**
   * Queue a route for preloading
   */
  private queueRoutePreload(route: string, priority: 'high' | 'medium' | 'low'): void {
    if (this.preloadedRoutes.has(route) || this.preloadQueue.has(route)) {
      return; // Already preloaded or queued
    }

    this.preloadQueue.add(route);

    // Execute preload based on priority
    const delay = this.getPriorityDelay(priority);
    setTimeout(() => {
      this.executeRoutePreload(route);
    }, delay);
  }

  /**
   * Execute route preloading
   */
  private async executeRoutePreload(route: string): Promise<void> {
    try {
      // Use router's preloader if available
      if ('preloader' in this.router && typeof (this.router as any).preloader?.preload === 'function') {
        await (this.router as any).preloader.preload(route, () => Promise.resolve());
      } else {
        // Fallback: create a link element for preloading
        this.createPreloadLink(route);
      }

      this.preloadedRoutes.add(route);
      this.preloadQueue.delete(route);
    } catch (error) {
      this.preloadQueue.delete(route);
    }
  }

  /**
   * Create preload link element
   */
  private createPreloadLink(route: string): void {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = route;
    link.as = 'document';

    // Add to head
    document.head.appendChild(link);

    // Remove after loading to clean up DOM
    link.onload = () => {
      setTimeout(() => {
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      }, 1000);
    };
  }

  /**
   * Get delay based on priority
   */
  private getPriorityDelay(priority: 'high' | 'medium' | 'low'): number {
    switch (priority) {
      case 'high': return 0;
      case 'medium': return 500;
      case 'low': return 2000;
      default: return 1000;
    }
  }

  /**
   * Setup route change tracking
   */
  private setupRouteTracking(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        debounceTime(100), // Debounce rapid navigation changes
        takeUntilDestroyed()
      )
      .subscribe((event: NavigationEnd) => {
        this.preloadStrategicRoutes(event.urlAfterRedirects);
      });
  }

  /**
   * Get preloading statistics
   */
  getPreloadingStats(): {
    queued: number;
    preloaded: number;
    routes: string[];
  } {
    return {
      queued: this.preloadQueue.size,
      preloaded: this.preloadedRoutes.size,
      routes: Array.from(this.preloadedRoutes)
    };
  }

  /**
   * Clear preloading cache
   */
  clearCache(): void {
    this.preloadQueue.clear();
    this.preloadedRoutes.clear();
    this.hoverTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
    this.hoverTimeouts.clear();
  }
}
