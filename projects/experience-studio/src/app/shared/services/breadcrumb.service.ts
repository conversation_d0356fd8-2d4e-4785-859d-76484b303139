import { Injectable, inject, signal, computed } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter, map, startWith } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouteConfigService } from './route-config.service';
import { createLogger } from '../utils';

/**
 * ENTERPRISE: Breadcrumb navigation service with Angular 19+ Signals
 * Provides dynamic breadcrumb generation based on current route
 */
export interface BreadcrumbItem {
  label: string;
  route?: string;
  icon?: string;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbService {
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly routeConfigService = inject(RouteConfigService);
  private readonly logger = createLogger('BreadcrumbService');

  // ENHANCED: Angular 19+ Signals for reactive breadcrumb state
  private readonly currentRoute = signal<string>('');
  private readonly routeData = signal<any>({});
  private readonly routeParams = signal<any>({});

  // ENHANCED: Computed breadcrumbs that automatically update when route changes
  readonly breadcrumbs = computed(() => {
    const route = this.currentRoute();
    const data = this.routeData();
    const params = this.routeParams();
    
    return this.generateBreadcrumbs(route, data, params);
  });

  // ENHANCED: Computed navigation state
  readonly canGoBack = computed(() => this.breadcrumbs().length > 1);
  readonly canGoHome = computed(() => !this.currentRoute().includes('/main'));

  constructor() {
    this.initializeRouteTracking();
  }

  /**
   * Initialize route tracking with Angular 19+ patterns
   */
  private initializeRouteTracking(): void {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.getRouteData()),
      startWith(this.getRouteData()),
      takeUntilDestroyed()
    ).subscribe(({ route, data, params }) => {
      this.currentRoute.set(route);
      this.routeData.set(data);
      this.routeParams.set(params);
      
      this.logger.info('Route updated:', { route, data, params });
    });
  }

  /**
   * Extract route data from activated route
   */
  private getRouteData(): { route: string; data: any; params: any } {
    let route = this.activatedRoute;
    let routePath = '';
    let routeData = {};
    let routeParams = {};

    // Traverse the route tree to get complete data
    while (route) {
      if (route.snapshot.url.length > 0) {
        routePath += '/' + route.snapshot.url.map(segment => segment.path).join('/');
      }
      
      routeData = { ...routeData, ...route.snapshot.data };
      routeParams = { ...routeParams, ...route.snapshot.params };
      
      route = route.firstChild;
    }

    return {
      route: routePath || this.router.url,
      data: routeData,
      params: routeParams
    };
  }

  /**
   * Generate breadcrumbs based on current route
   */
  private generateBreadcrumbs(route: string, data: any, params: any): BreadcrumbItem[] {
    const breadcrumbs: BreadcrumbItem[] = [];
    
    // Always start with home
    breadcrumbs.push({
      label: 'Home',
      route: '/experience/main',
      icon: 'home',
      isActive: route === '/experience/main'
    });

    // Skip if we're already on home
    if (route === '/experience/main' || route === '/experience') {
      return breadcrumbs;
    }

    // Parse route segments
    const segments = route.split('/').filter(segment => segment && segment !== 'experience');
    
    this.logger.debug('Generating breadcrumbs for segments:', segments);

    // Build breadcrumbs based on route structure
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      const isLast = i === segments.length - 1;
      
      const breadcrumb = this.createBreadcrumbForSegment(
        segment, 
        segments.slice(0, i + 1), 
        data, 
        params, 
        isLast
      );
      
      if (breadcrumb) {
        breadcrumbs.push(breadcrumb);
      }
    }

    return breadcrumbs;
  }

  /**
   * Create breadcrumb item for a route segment
   */
  private createBreadcrumbForSegment(
    segment: string,
    pathSegments: string[],
    data: any,
    params: any,
    isLast: boolean
  ): BreadcrumbItem | null {
    
    // Handle project types
    if (segment === 'ui-design') {
      return {
        label: 'UI Design',
        route: isLast ? undefined : '/experience/ui-design/new',
        icon: 'palette',
        isActive: isLast
      };
    }
    
    if (segment === 'application') {
      return {
        label: 'Application',
        route: isLast ? undefined : '/experience/application/new',
        icon: 'code',
        isActive: isLast
      };
    }
    
    if (segment === 'projects') {
      return {
        label: 'Projects',
        route: isLast ? undefined : '/experience/main',
        icon: 'folder',
        isActive: isLast
      };
    }

    // Handle actions
    if (segment === 'new') {
      return {
        label: 'New',
        icon: 'plus',
        isActive: isLast
      };
    }
    
    if (segment === 'edit') {
      return {
        label: 'Edit',
        icon: 'edit',
        isActive: isLast
      };
    }
    
    if (segment === 'preview') {
      return {
        label: 'Preview',
        icon: 'eye',
        isActive: isLast
      };
    }
    
    if (segment === 'share') {
      return {
        label: 'Share',
        icon: 'share',
        isActive: isLast
      };
    }

    // Handle project IDs (UUIDs)
    if (this.isProjectId(segment)) {
      const projectName = this.getProjectName(segment, data);
      return {
        label: projectName || 'Project',
        route: isLast ? undefined : this.buildProjectRoute(pathSegments, segment),
        icon: 'file',
        isActive: isLast,
        metadata: { projectId: segment }
      };
    }

    // Handle other segments
    return {
      label: this.formatSegmentLabel(segment),
      isActive: isLast
    };
  }

  /**
   * Check if segment is a project ID
   */
  private isProjectId(segment: string): boolean {
    // UUID pattern or project ID pattern
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const projectIdPattern = /^proj-[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    return uuidPattern.test(segment) || projectIdPattern.test(segment);
  }

  /**
   * Get project name from route data or fallback
   */
  private getProjectName(projectId: string, data: any): string | null {
    // Try to get from resolved data
    if (data?.projectData?.project?.project_name) {
      return data.projectData.project.project_name;
    }
    
    // Try to get from metadata
    if (data?.metadata?.title && data.metadata.title !== 'Experience Studio') {
      return data.metadata.title;
    }
    
    return null;
  }

  /**
   * Build project route for breadcrumb navigation
   */
  private buildProjectRoute(pathSegments: string[], projectId: string): string {
    const projectTypeSegment = pathSegments.find(s => s === 'ui-design' || s === 'application' || s === 'projects');
    
    if (projectTypeSegment === 'projects') {
      return `/experience/projects/${projectId}`;
    } else if (projectTypeSegment) {
      return `/experience/${projectTypeSegment}/${projectId}`;
    }
    
    return `/experience/projects/${projectId}`;
  }

  /**
   * Format segment label for display
   */
  private formatSegmentLabel(segment: string): string {
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // ===== PUBLIC API =====

  /**
   * Navigate to a breadcrumb item
   */
  navigateTo(breadcrumb: BreadcrumbItem): void {
    if (breadcrumb.route) {
      this.logger.info('Navigating to breadcrumb:', breadcrumb.label);
      this.router.navigate([breadcrumb.route]);
    }
  }

  /**
   * Navigate back to previous breadcrumb
   */
  goBack(): void {
    const crumbs = this.breadcrumbs();
    if (crumbs.length > 1) {
      const previousCrumb = crumbs[crumbs.length - 2];
      if (previousCrumb.route) {
        this.navigateTo(previousCrumb);
      }
    }
  }

  /**
   * Navigate to home
   */
  goHome(): void {
    this.router.navigate(['/experience/main']);
  }

  /**
   * Get current page title from breadcrumbs
   */
  getCurrentPageTitle(): string {
    const crumbs = this.breadcrumbs();
    return crumbs.length > 0 ? crumbs[crumbs.length - 1].label : 'Experience Studio';
  }

  /**
   * Get breadcrumb path as string
   */
  getBreadcrumbPath(): string {
    return this.breadcrumbs()
      .map(crumb => crumb.label)
      .join(' > ');
  }

  /**
   * Check if current route matches pattern
   */
  isCurrentRoute(pattern: string): boolean {
    const currentRoute = this.currentRoute();
    return currentRoute.includes(pattern);
  }

  /**
   * Get route metadata for current page
   */
  getCurrentRouteMetadata(): Record<string, any> {
    return this.routeData();
  }
}
