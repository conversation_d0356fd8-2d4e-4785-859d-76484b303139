import { Injectable } from '@angular/core';

/**
 * ENTERPRISE: UUID service for generating and validating unique identifiers
 * Provides secure, collision-resistant IDs for projects and sessions
 */
@Injectable({
  providedIn: 'root'
})
export class UuidService {

  /**
   * Generate a UUID v4 (random)
   * Uses crypto.randomUUID() when available, falls back to manual generation
   */
  generateUuid(): string {
    // Use native crypto.randomUUID() if available (modern browsers)
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }

    // Fallback to manual UUID generation
    return this.generateUuidManual();
  }

  /**
   * Manual UUID v4 generation for compatibility
   */
  private generateUuidManual(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Validate UUID format (v4)
   */
  isValidUuid(uuid: string): boolean {
    if (!uuid || typeof uuid !== 'string') {
      return false;
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Validate and normalize UUID (convert to lowercase)
   */
  normalizeUuid(uuid: string): string | null {
    if (!this.isValidUuid(uuid)) {
      return null;
    }
    return uuid.toLowerCase();
  }

  /**
   * Generate a short UUID for display purposes (first 8 characters)
   */
  generateShortId(): string {
    return this.generateUuid().substring(0, 8);
  }

  /**
   * Generate a project-specific UUID with prefix
   */
  generateProjectId(): string {
    return `proj-${this.generateUuid()}`;
  }

  /**
   * Generate a session-specific UUID with prefix
   */
  generateSessionId(): string {
    return `sess-${this.generateUuid()}`;
  }

  /**
   * Generate a user-specific UUID with prefix
   */
  generateUserId(): string {
    return `user-${this.generateUuid()}`;
  }

  /**
   * Extract UUID from prefixed ID
   */
  extractUuid(prefixedId: string): string | null {
    if (!prefixedId || typeof prefixedId !== 'string') {
      return null;
    }

    // Handle prefixed IDs (e.g., "proj-uuid", "sess-uuid")
    const parts = prefixedId.split('-');
    if (parts.length >= 6) { // UUID has 5 dashes, so 6 parts minimum
      const uuid = parts.slice(1).join('-'); // Remove first part (prefix)
      return this.isValidUuid(uuid) ? uuid : null;
    }

    // Handle direct UUID
    return this.isValidUuid(prefixedId) ? prefixedId : null;
  }

  /**
   * Generate a time-based UUID for ordering
   * Combines timestamp with random data for sortable UUIDs
   */
  generateTimeBasedId(): string {
    const timestamp = Date.now().toString(16).padStart(12, '0');
    const random = Math.random().toString(16).substring(2, 14).padStart(12, '0');
    
    return [
      timestamp.substring(0, 8),
      timestamp.substring(8, 12),
      '4' + random.substring(1, 4), // Version 4
      ((parseInt(random.substring(4, 5), 16) & 0x3) | 0x8).toString(16) + random.substring(5, 8),
      random.substring(8, 20)
    ].join('-');
  }

  /**
   * Extract timestamp from time-based UUID
   */
  extractTimestamp(timeBasedId: string): Date | null {
    if (!this.isValidUuid(timeBasedId)) {
      return null;
    }

    try {
      const parts = timeBasedId.split('-');
      const timestampHex = parts[0] + parts[1];
      const timestamp = parseInt(timestampHex, 16);
      
      // Validate timestamp is reasonable (after year 2000, before year 3000)
      if (timestamp < ************ || timestamp > 32503680000000) {
        return null;
      }
      
      return new Date(timestamp);
    } catch {
      return null;
    }
  }

  /**
   * Generate a collision-resistant ID for temporary use
   * Combines multiple entropy sources
   */
  generateTempId(): string {
    const timestamp = Date.now();
    const random = Math.random();
    const performance = typeof performance !== 'undefined' ? performance.now() : 0;
    
    const combined = `${timestamp}-${random}-${performance}`;
    return btoa(combined).replace(/[+/=]/g, '').substring(0, 16);
  }

  /**
   * Validate project ID format (with or without prefix)
   */
  isValidProjectId(projectId: string): boolean {
    if (!projectId || typeof projectId !== 'string') {
      return false;
    }

    // Handle prefixed project IDs
    if (projectId.startsWith('proj-')) {
      const uuid = projectId.substring(5);
      return this.isValidUuid(uuid);
    }

    // Handle direct UUIDs
    return this.isValidUuid(projectId);
  }

  /**
   * Generate a human-readable ID for sharing
   * Uses a combination of words and numbers
   */
  generateReadableId(): string {
    const adjectives = ['quick', 'bright', 'smart', 'cool', 'fast', 'neat', 'bold', 'calm'];
    const nouns = ['fox', 'cat', 'dog', 'bird', 'fish', 'bear', 'wolf', 'lion'];
    const numbers = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    
    return `${adjective}-${noun}-${numbers}`;
  }

  /**
   * Batch generate multiple UUIDs
   */
  generateBatch(count: number): string[] {
    if (count <= 0 || count > 1000) {
      throw new Error('Batch count must be between 1 and 1000');
    }

    return Array.from({ length: count }, () => this.generateUuid());
  }

  /**
   * Check for UUID collisions in a given array
   */
  checkCollisions(uuids: string[]): boolean {
    const uniqueUuids = new Set(uuids);
    return uniqueUuids.size !== uuids.length;
  }

  /**
   * Generate a namespace-specific UUID (deterministic)
   * Note: This is a simple implementation, for production use a proper UUID v5 library
   */
  generateNamespaceUuid(namespace: string, name: string): string {
    // Simple hash-based UUID generation (not cryptographically secure)
    const combined = `${namespace}:${name}`;
    let hash = 0;
    
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    const hashStr = Math.abs(hash).toString(16).padStart(8, '0');
    const random = Math.random().toString(16).substring(2, 14);
    
    return [
      hashStr,
      random.substring(0, 4),
      '5' + random.substring(4, 7), // Version 5
      ((parseInt(random.substring(7, 8), 16) & 0x3) | 0x8).toString(16) + random.substring(8, 11),
      random.substring(11, 23).padEnd(12, '0')
    ].join('-');
  }
}
